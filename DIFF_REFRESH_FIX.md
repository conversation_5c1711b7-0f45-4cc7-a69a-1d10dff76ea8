# Diff窗口刷新问题修复

## 问题描述

使用`showOriginalDiff`后，当对比的内容发生变更之后（比如选择了discard），已经打开的diff界面不会重新刷新。

## 问题分析

1. **原始问题**：
   - `showOriginalDiff`方法每次都创建新的diff窗口
   - 当文件内容发生变化（如discard操作）后，已打开的diff窗口不会自动更新
   - 用户需要手动关闭旧窗口并重新打开才能看到最新的差异

2. **根本原因**：
   - 缺少对已存在diff窗口的检测和管理机制
   - 没有在内容变更时刷新已打开的diff窗口

## 解决方案

### 1. 修改FileOriginalVersionService类

#### 添加diff窗口跟踪机制
```kotlin
// 跟踪已打开的diff窗口，使用WeakReference避免内存泄漏
private val activeDiffWindows = ConcurrentHashMap<String, WeakReference<Any>>()
```

#### 新增方法

1. **`closeExistingDiffWindows(filePath: String)`**
   - 关闭指定文件的所有相关diff窗口
   - 使用`FileEditorManager.getAllEditors()`查找已打开的编辑器
   - 识别并关闭与指定文件相关的diff编辑器

2. **`isDiffEditorForFile(editor: FileEditor, filePath: String, expectedTitle: String)`**
   - 检查编辑器是否是指定文件的diff编辑器
   - 通过编辑器类名和标题进行匹配

3. **`createAndShowDiff(filePath: String, originalContent: String, currentContent: String)`**
   - 创建并显示新的diff窗口
   - 统一的diff创建逻辑

#### 修改现有方法

1. **`showOriginalDiff`方法**：
   ```kotlin
   fun showOriginalDiff(filePath: String, originContent: String? = null): Boolean {
       // ... 获取内容逻辑 ...
       
       // 关闭已存在的diff窗口（如果有的话）
       closeExistingDiffWindows(filePath)

       // 创建新的diff窗口
       ApplicationManager.getApplication().invokeLater {
           createAndShowDiff(filePath, readOriginContent, currentContent)
           logger.info("Created new diff window for file: $filePath")
       }

       return true
   }
   ```

2. **`clearOriginalVersion`方法**：
   ```kotlin
   fun clearOriginalVersion(filePath: String) {
       fileVersions.remove(filePath)
       // 同时清理diff窗口跟踪
       activeDiffWindows.remove(filePath)
   }
   ```

### 2. 实现策略

采用"关闭旧窗口并重新创建"的策略，而不是尝试直接更新现有窗口内容。这种方法的优势：

1. **可靠性高**：避免了复杂的diff窗口内容更新API
2. **兼容性好**：不依赖于IntelliJ内部的diff实现细节
3. **用户体验**：用户看到的是最新的差异内容

### 3. 日志增强

添加了详细的日志记录，便于调试和监控：
- 记录diff窗口的创建和关闭
- 记录编辑器查找和匹配过程
- 记录刷新操作的成功/失败状态

## 修改文件

1. **主要修改**：
   - `src/main/kotlin/com/think1024/tocodesign/ideaplugin/services/FileOriginalVersionService.kt`

2. **新增文件**：
   - `src/main/kotlin/com/think1024/tocodesign/ideaplugin/demo/DiffRefreshDemo.kt` (演示和测试用)

## 测试验证

### 手动测试步骤

1. 打开一个文件并进行修改
2. 调用`showOriginalDiff`显示差异
3. 对文件进行discard操作或其他修改
4. 再次调用`showOriginalDiff`
5. 验证diff窗口是否显示最新的差异内容

### 预期结果

- 第一次调用`showOriginalDiff`：创建新的diff窗口
- 文件内容变更后再次调用：关闭旧窗口，创建显示最新差异的新窗口
- 用户始终看到最新的文件差异对比

## 兼容性

- 保持了原有API的兼容性
- 不影响现有功能的使用
- 向后兼容所有现有调用

## 性能考虑

- 使用`WeakReference`避免内存泄漏
- 使用`ConcurrentHashMap`保证线程安全
- 在EDT线程中执行UI操作，避免线程问题

## 总结

此修复解决了diff窗口不刷新的问题，确保用户在文件内容变更后能够看到最新的差异对比。通过采用"关闭并重新创建"的策略，提供了可靠且用户友好的解决方案。
