# Diff窗口刷新问题修复

## 问题描述

使用`showOriginalDiff`后，当对比的内容发生变更之后（比如选择了discard），已经打开的diff界面不会重新刷新。

## 问题分析

1. **原始问题**：
   - `showOriginalDiff`方法每次都创建新的diff窗口
   - 当文件内容发生变化（如discard操作）后，已打开的diff窗口不会自动更新
   - 用户需要手动关闭旧窗口并重新打开才能看到最新的差异

2. **根本原因**：
   - 缺少对已存在diff窗口的检测和管理机制
   - 没有在内容变更时刷新已打开的diff窗口

3. **用户真正需求**：
   - **刷新已存在的diff窗口内容**，而不是关闭并重新创建窗口
   - 保持用户在diff窗口中的操作状态（滚动位置、选择等）

## 解决方案

### 1. 修改FileOriginalVersionService类

#### 添加diff窗口跟踪机制
```kotlin
// 跟踪已打开的diff窗口和对应的DiffRequest，使用WeakReference避免内存泄漏
private val activeDiffRequests = ConcurrentHashMap<String, WeakReference<SimpleDiffRequest>>()
```

#### 新增方法

1. **`refreshExistingDiffWindow(filePath: String, originalContent: String, currentContent: String)`**
   - 查找并刷新已存在的diff窗口
   - 优先尝试通过保存的DiffRequest刷新
   - 备用方案：通过FileEditorManager查找diff编辑器

2. **`refreshDiffRequestContent(diffRequest: SimpleDiffRequest, originalContent: String, currentContent: String, filePath: String)`**
   - 刷新DiffRequest的内容
   - 查找对应的DiffViewer并更新

3. **`tryRefreshDiffViewerContent(editor: FileEditor, originalContent: String, currentContent: String, filePath: String)`**
   - 尝试直接刷新DiffViewer的内容
   - 支持SimpleDiffViewer和UnifiedDiffViewer

4. **`refreshDiffViewerDirectly(diffViewer: FrameDiffTool.DiffViewer, originalContent: String, currentContent: String, filePath: String)`**
   - 直接更新DiffViewer的文档内容
   - 调用`rediff()`方法重新计算差异

5. **`createAndShowDiff(filePath: String, originalContent: String, currentContent: String)`**
   - 创建并显示新的diff窗口
   - 保存DiffRequest引用以便后续刷新

#### 修改现有方法

1. **`showOriginalDiff`方法**：
   ```kotlin
   fun showOriginalDiff(filePath: String, originContent: String? = null): Boolean {
       // ... 获取内容逻辑 ...

       // 首先尝试刷新已存在的diff窗口
       if (refreshExistingDiffWindow(filePath, readOriginContent, currentContent)) {
           logger.info("Successfully refreshed existing diff window for file: $filePath")
           return true
       }

       // 如果没有找到已存在的diff窗口或刷新失败，创建新的
       ApplicationManager.getApplication().invokeLater {
           createAndShowDiff(filePath, readOriginContent, currentContent)
           logger.info("Created new diff window for file: $filePath")
       }

       return true
   }
   ```

2. **`clearOriginalVersion`方法**：
   ```kotlin
   fun clearOriginalVersion(filePath: String) {
       fileVersions.remove(filePath)
       // 同时清理diff窗口跟踪
       activeDiffRequests.remove(filePath)
   }
   ```

### 2. 实现策略

采用**"优先刷新，备用重建"**的策略：

1. **主要策略**：尝试直接刷新已存在的diff窗口内容
   - 通过保存的DiffRequest引用查找对应的DiffViewer
   - 直接更新DiffViewer的文档内容
   - 调用`rediff()`方法重新计算差异
   - 保持用户的操作状态（滚动位置、选择等）

2. **备用策略**：如果无法直接刷新，则关闭并重新创建窗口
   - 确保在任何情况下都能显示最新的差异内容
   - 提供可靠的后备方案

3. **优势**：
   - **用户体验优先**：保持用户在diff窗口中的操作状态
   - **性能优化**：避免不必要的窗口重建
   - **可靠性保证**：备用方案确保功能始终可用

### 3. 日志增强

添加了详细的日志记录，便于调试和监控：
- 记录diff窗口的创建和关闭
- 记录编辑器查找和匹配过程
- 记录刷新操作的成功/失败状态

## 修改文件

1. **主要修改**：
   - `src/main/kotlin/com/think1024/tocodesign/ideaplugin/services/FileOriginalVersionService.kt`

2. **新增文件**：
   - `src/main/kotlin/com/think1024/tocodesign/ideaplugin/demo/DiffRefreshDemo.kt` (演示和测试用)

## 测试验证

### 手动测试步骤

1. 打开一个文件并进行修改
2. 调用`showOriginalDiff`显示差异
3. 对文件进行discard操作或其他修改
4. 再次调用`showOriginalDiff`
5. 验证diff窗口是否显示最新的差异内容

### 预期结果

- 第一次调用`showOriginalDiff`：创建新的diff窗口
- 文件内容变更后再次调用：
  - **优先**：刷新已存在的diff窗口内容，保持用户操作状态
  - **备用**：如果刷新失败，则重新创建窗口
- 用户始终看到最新的文件差异对比，且尽可能保持操作状态

## 兼容性

- 保持了原有API的兼容性
- 不影响现有功能的使用
- 向后兼容所有现有调用

## 性能考虑

- 使用`WeakReference`避免内存泄漏
- 使用`ConcurrentHashMap`保证线程安全
- 在EDT线程中执行UI操作，避免线程问题

## 重要修复：自动清理机制

### 问题识别
用户指出了一个关键问题：**当用户手动关闭diff窗口时，`activeDiffRequests`中的引用没有及时清除**，这会导致：

1. **内存泄漏**：即使使用了`WeakReference`，Map本身的键值对不会自动清理
2. **状态不一致**：系统认为diff窗口还存在，但实际已被关闭
3. **功能异常**：可能尝试刷新已关闭的窗口

### 解决方案

#### 1. 文件编辑器监听器
```kotlin
private fun setupFileEditorListener() {
    messageBusConnection = project.messageBus.connect()
    messageBusConnection?.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
        override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
            // 当文件关闭时，检查并清理相关的diff请求
            cleanupDiffRequestsForFile(file)
        }
    })
}
```

#### 2. 自动清理失效引用
```kotlin
private fun cleanupStaleReferences() {
    val keysToRemove = mutableListOf<String>()

    for ((key, requestRef) in activeDiffRequests) {
        if (requestRef.get() == null) {
            keysToRemove.add(key)
        }
    }

    for (key in keysToRemove) {
        activeDiffRequests.remove(key)
    }
}
```

#### 3. 服务生命周期管理
```kotlin
fun dispose() {
    // 断开消息总线连接
    messageBusConnection?.disconnect()
    messageBusConnection = null

    // 清空diff请求映射
    activeDiffRequests.clear()
}
```

## 总结

此修复解决了diff窗口不刷新的问题，确保用户在文件内容变更后能够看到最新的差异对比。通过采用**"优先刷新，备用重建"**的策略，既保证了用户体验（保持操作状态），又确保了功能的可靠性。

### 关键改进

1. **理解用户真正需求**：刷新窗口内容而不是重新创建窗口
2. **保持用户操作状态**：滚动位置、选择内容等不会丢失
3. **提供可靠的备用方案**：确保在任何情况下都能正常工作
4. **优化性能**：避免不必要的窗口重建操作
5. **完善的资源管理**：自动清理关闭的diff窗口引用，防止内存泄漏
6. **状态一致性**：确保内部状态与实际UI状态保持同步
