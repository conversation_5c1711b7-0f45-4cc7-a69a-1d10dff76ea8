package com.think1024.tocodesign.ideaplugin.demo

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.services.FileOriginalVersionService

/**
 * 演示diff窗口刷新功能的类
 * 这个类用于验证修复后的showOriginalDiff方法能够正确刷新已存在的diff窗口
 */
class DiffRefreshDemo {
    private val logger = Logger.getInstance(DiffRefreshDemo::class.java)

    /**
     * 演示diff窗口刷新功能
     * 
     * @param project 项目实例
     * @param filePath 要演示的文件路径
     */
    fun demonstrateDiffRefresh(project: Project, filePath: String) {
        logger.info("开始演示diff窗口刷新功能，文件: $filePath")
        
        val service = FileOriginalVersionService.getInstance(project)
        
        // 步骤1: 记录原始版本
        val recorded = service.recordOriginalVersion(filePath)
        if (recorded) {
            logger.info("✅ 成功记录文件原始版本: $filePath")
        } else {
            logger.info("ℹ️ 文件原始版本已存在: $filePath")
        }
        
        // 步骤2: 第一次显示diff
        ApplicationManager.getApplication().invokeLater {
            val firstShow = service.showOriginalDiff(filePath)
            if (firstShow) {
                logger.info("✅ 第一次显示diff成功: $filePath")
                
                // 步骤3: 延迟后再次显示diff（模拟文件内容变更后的刷新）
                ApplicationManager.getApplication().invokeLater {
                    Thread.sleep(2000) // 等待2秒，让用户看到第一个diff窗口
                    
                    val secondShow = service.showOriginalDiff(filePath)
                    if (secondShow) {
                        logger.info("✅ 第二次显示diff成功（应该刷新了已存在的窗口）: $filePath")
                    } else {
                        logger.warn("❌ 第二次显示diff失败: $filePath")
                    }
                }
            } else {
                logger.warn("❌ 第一次显示diff失败: $filePath")
            }
        }
    }
    
    /**
     * 验证diff窗口关闭功能
     */
    fun demonstrateCloseExistingDiff(project: Project, filePath: String) {
        logger.info("开始演示diff窗口关闭功能，文件: $filePath")
        
        val service = FileOriginalVersionService.getInstance(project)
        
        // 确保有原始版本记录
        service.recordOriginalVersion(filePath)
        
        // 显示diff
        ApplicationManager.getApplication().invokeLater {
            service.showOriginalDiff(filePath)
            logger.info("✅ 显示了diff窗口: $filePath")
            
            // 延迟后清除原始版本（这应该会清理相关的diff窗口跟踪）
            ApplicationManager.getApplication().invokeLater {
                Thread.sleep(3000) // 等待3秒
                
                service.clearOriginalVersion(filePath)
                logger.info("✅ 清除了原始版本记录: $filePath")
                
                // 再次尝试显示diff（应该失败，因为没有原始版本）
                val result = service.showOriginalDiff(filePath)
                if (!result) {
                    logger.info("✅ 正确：清除原始版本后无法显示diff: $filePath")
                } else {
                    logger.warn("❌ 意外：清除原始版本后仍能显示diff: $filePath")
                }
            }
        }
    }
    
    companion object {
        /**
         * 创建演示实例
         */
        fun create(): DiffRefreshDemo {
            return DiffRefreshDemo()
        }
    }
}
