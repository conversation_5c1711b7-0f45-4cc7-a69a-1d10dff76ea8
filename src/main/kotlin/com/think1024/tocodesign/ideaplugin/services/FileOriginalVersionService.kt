package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.util.DiffDataKeys
import com.intellij.diff.util.DiffUserDataKeys
import com.intellij.diff.FrameDiffTool
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.util.messages.MessageBusConnection
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.concurrent.ConcurrentHashMap
import java.lang.ref.WeakReference

/**
 * 存储文件原始版本的服务类
 */
@Service(Service.Level.PROJECT)
class FileOriginalVersionService(private val project: Project) {
    private val logger = Logger.getInstance(FileOriginalVersionService::class.java)
    private val fileVersions = ConcurrentHashMap<String, String>()

    // 跟踪已打开的diff窗口和对应的DiffRequest，使用WeakReference避免内存泄漏
    private val activeDiffRequests = ConcurrentHashMap<String, WeakReference<SimpleDiffRequest>>()

    // 消息总线连接，用于监听文件编辑器事件
    private var messageBusConnection: MessageBusConnection? = null

    init {
        // 注册文件编辑器监听器，用于清理关闭的diff窗口
        setupFileEditorListener()
    }

    /**
     * 记录文件的原始版本（如果尚未记录）
     *
     * @param filePath 文件路径
     * @return 是否成功记录（如果已存在记录则返回false）
     */
    fun recordOriginalVersion(filePath: String): Boolean {
        // 如果已经记录过该文件的原始版本，则不再记录
        if (fileVersions.containsKey(filePath)) {
            return false
        }

        // 获取文件当前内容
        val codeFileService = CodeFileService.getInstance(project)
        // 如果拿不到内容，意味着新文件
        val content = codeFileService.getFileContent(filePath) ?: ""

        // 记录原始版本
        fileVersions[filePath] = content
        logger.info("记录文件原始版本: $filePath")
        logger.info("记录文件原始内容: $content")

        return true
    }

    /**
     * 获取文件的原始版本内容
     *
     * @param filePath 文件路径
     * @return 原始版本内容，如果不存在则返回null
     */
    fun getOriginalVersion(filePath: String): String? {
        return fileVersions[filePath]
    }

    /**
     * 查找并刷新已存在的diff窗口
     *
     * @param filePath 文件路径
     * @param originalContent 原始内容
     * @param currentContent 当前内容
     * @return 是否找到并成功刷新了已存在的diff窗口
     */
    private fun refreshExistingDiffWindow(filePath: String, originalContent: String, currentContent: String): Boolean {
        try {
            // 先清理无效的引用
            cleanupStaleReferences()

            // 首先尝试通过已保存的DiffRequest刷新
            val diffRequestRef = activeDiffRequests[filePath]
            val diffRequest = diffRequestRef?.get()

            if (diffRequest != null) {
                logger.info("Found existing DiffRequest for file: $filePath, attempting to refresh")
                return refreshDiffRequestContent(diffRequest, originalContent, currentContent, filePath)
            } else if (diffRequestRef != null) {
                // 如果引用存在但对象已被GC，清理该条目
                activeDiffRequests.remove(filePath)
                logger.debug("Removed stale diff request reference for file: $filePath")
            }

            // 如果没有保存的DiffRequest，尝试通过FileEditorManager查找
            return findAndRefreshDiffEditor(filePath, originalContent, currentContent)

        } catch (e: Exception) {
            logger.warn("Failed to refresh existing diff window for file: $filePath", e)
        }
        return false
    }

    /**
     * 清理已失效的WeakReference
     */
    private fun cleanupStaleReferences() {
        val keysToRemove = mutableListOf<String>()

        for ((key, requestRef) in activeDiffRequests) {
            if (requestRef.get() == null) {
                keysToRemove.add(key)
            }
        }

        for (key in keysToRemove) {
            activeDiffRequests.remove(key)
        }

        if (keysToRemove.isNotEmpty()) {
            logger.debug("Cleaned up ${keysToRemove.size} stale diff request references")
        }
    }

    /**
     * 刷新DiffRequest的内容
     */
    private fun refreshDiffRequestContent(diffRequest: SimpleDiffRequest, originalContent: String, currentContent: String, filePath: String): Boolean {
        try {
            ApplicationManager.getApplication().invokeLater {
                // 更新DiffRequest的内容
                val newContent1 = DiffContentFactory.getInstance().create(originalContent)
                val newContent2 = DiffContentFactory.getInstance().create(currentContent)

                // 由于SimpleDiffRequest的内容是不可变的，我们需要通过其他方式刷新
                // 这里我们尝试找到对应的DiffViewer并刷新
                findAndRefreshDiffViewer(filePath, originalContent, currentContent)
            }
            return true
        } catch (e: Exception) {
            logger.warn("Failed to refresh DiffRequest content for file: $filePath", e)
            return false
        }
    }

    /**
     * 查找并刷新DiffViewer
     */
    private fun findAndRefreshDiffViewer(filePath: String, originalContent: String, currentContent: String): Boolean {
        try {
            val fileEditorManager = FileEditorManager.getInstance(project)
            val editors = fileEditorManager.allEditors

            val fileName = FileUtil.getVirtualFile(filePath, project)?.name ?: return false
            val diffTitle = "${getI18nString("code.diff.title.file.diff")}: $fileName"

            // 查找与该文件相关的diff编辑器
            for (editor in editors) {
                if (isDiffEditorForFile(editor, filePath, diffTitle)) {
                    logger.info("Found matching diff editor for file: $filePath")
                    return refreshDiffEditor(editor, originalContent, currentContent, filePath)
                }
            }

            logger.debug("No existing diff editor found for file: $filePath")
        } catch (e: Exception) {
            logger.warn("Failed to find and refresh diff viewer for file: $filePath", e)
        }
        return false
    }

    /**
     * 通过FileEditorManager查找并刷新diff编辑器
     */
    private fun findAndRefreshDiffEditor(filePath: String, originalContent: String, currentContent: String): Boolean {
        return findAndRefreshDiffViewer(filePath, originalContent, currentContent)
    }

    /**
     * 检查编辑器是否是指定文件的diff编辑器
     */
    private fun isDiffEditorForFile(editor: FileEditor, filePath: String, expectedTitle: String): Boolean {
        try {
            // 检查编辑器的类名是否包含diff相关信息
            val editorClassName = editor.javaClass.simpleName
            logger.debug("Checking editor: $editorClassName, name: ${editor.name}")

            if (editorClassName.contains("Diff", ignoreCase = true)) {
                // 进一步检查是否与指定文件相关
                val fileName = FileUtil.getVirtualFile(filePath, project)?.name ?: return false
                val editorName = editor.name

                // 检查编辑器名称是否包含文件名或预期的标题
                return editorName.contains(fileName) || editorName.contains(expectedTitle)
            }
        } catch (e: Exception) {
            logger.debug("Error checking if editor is diff editor for file: $filePath", e)
        }
        return false
    }

    /**
     * 刷新diff编辑器的内容
     */
    private fun refreshDiffEditor(editor: FileEditor, originalContent: String, currentContent: String, filePath: String): Boolean {
        try {
            logger.info("Attempting to refresh diff editor content for file: $filePath")

            // 尝试获取DiffViewer实例并刷新内容
            val refreshed = tryRefreshDiffViewerContent(editor, originalContent, currentContent, filePath)

            if (refreshed) {
                logger.info("Successfully refreshed diff editor content for file: $filePath")
                return true
            } else {
                logger.info("Could not refresh diff editor content directly, will recreate window for file: $filePath")
                // 如果无法直接刷新，则关闭并重新创建
                return recreateDiffWindow(editor, originalContent, currentContent, filePath)
            }

        } catch (e: Exception) {
            logger.warn("Failed to refresh diff editor for file: $filePath", e)
            return false
        }
    }

    /**
     * 尝试直接刷新DiffViewer的内容
     */
    private fun tryRefreshDiffViewerContent(editor: FileEditor, originalContent: String, currentContent: String, filePath: String): Boolean {
        try {
            // 这里需要通过反射或其他方式获取DiffViewer实例
            // 由于IntelliJ的diff API比较复杂，我们先尝试一个简单的方法

            // 检查编辑器是否包含DiffViewer相关的组件
            val editorComponent = editor.component
            if (editorComponent != null) {
                // 尝试查找DiffViewer组件
                val diffViewer = findDiffViewerInComponent(editorComponent)
                if (diffViewer != null) {
                    return refreshDiffViewerDirectly(diffViewer, originalContent, currentContent, filePath)
                }
            }

            return false
        } catch (e: Exception) {
            logger.debug("Could not refresh diff viewer content directly for file: $filePath", e)
            return false
        }
    }

    /**
     * 在组件中查找DiffViewer
     */
    private fun findDiffViewerInComponent(component: java.awt.Component): FrameDiffTool.DiffViewer? {
        // 这是一个简化的实现，实际情况可能需要更复杂的查找逻辑
        // 由于IntelliJ的内部结构比较复杂，这里返回null，使用备用方案
        return null
    }

    /**
     * 直接刷新DiffViewer
     */
    private fun refreshDiffViewerDirectly(diffViewer: FrameDiffTool.DiffViewer, originalContent: String, currentContent: String, filePath: String): Boolean {
        try {
            // 如果是SimpleDiffViewer，尝试更新内容
            if (diffViewer is SimpleDiffViewer) {
                ApplicationManager.getApplication().invokeLater {
                    WriteCommandAction.runWriteCommandAction(project) {
                        // 更新左侧内容（原始版本）
                        val leftDocument = diffViewer.editor1.document
                        leftDocument.setText(originalContent)

                        // 更新右侧内容（当前版本）
                        val rightDocument = diffViewer.editor2.document
                        rightDocument.setText(currentContent)

                        // 触发重新计算差异
                        diffViewer.rediff()
                    }
                }
                return true
            } else if (diffViewer is UnifiedDiffViewer) {
                ApplicationManager.getApplication().invokeLater {
                    WriteCommandAction.runWriteCommandAction(project) {
                        // 对于UnifiedDiffViewer，更新内容并重新计算差异
                        diffViewer.rediff()
                    }
                }
                return true
            }

            return false
        } catch (e: Exception) {
            logger.warn("Failed to refresh diff viewer directly for file: $filePath", e)
            return false
        }
    }

    /**
     * 重新创建diff窗口（备用方案）
     */
    private fun recreateDiffWindow(editor: FileEditor, originalContent: String, currentContent: String, filePath: String): Boolean {
        try {
            // 关闭当前的diff编辑器
            val fileEditorManager = FileEditorManager.getInstance(project)
            val file = editor.file
            if (file != null) {
                fileEditorManager.closeFile(file)
                logger.info("Closed existing diff editor for file: $filePath")
            }

            // 重新创建并显示diff
            ApplicationManager.getApplication().invokeLater {
                createAndShowDiff(filePath, originalContent, currentContent)
                logger.info("Recreated diff window for file: $filePath")
            }

            return true
        } catch (e: Exception) {
            logger.warn("Failed to recreate diff window for file: $filePath", e)
            return false
        }
    }

    /**
     * 创建并显示diff窗口
     */
    private fun createAndShowDiff(filePath: String, originalContent: String, currentContent: String) {
        val diffRequest = SimpleDiffRequest(
            "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(filePath, project)?.name ?: filePath}",
            DiffContentFactory.getInstance().create(originalContent),
            DiffContentFactory.getInstance().create(currentContent),
            getI18nString("code.diff.origin.version"),
            getI18nString("code.diff.current.version"),
        )

        // 保存DiffRequest引用以便后续刷新
        activeDiffRequests[filePath] = WeakReference(diffRequest)

        DiffManager.getInstance().showDiff(project, diffRequest)
    }



    /**
     * 显示指定文件的原始版本和当前版本的差异
     *
     * @param filePath 文件路径
     * @return 是否成功显示差异（如果没有原始版本则返回false）
     */
    fun showOriginalDiff(filePath: String, originContent: String? = null): Boolean {
        // 获取原始版本
        val readOriginContent = originContent ?: fileVersions[filePath] ?: return false

        // 用于测试
        if (originContent != null && fileVersions[filePath] == null) {
            fileVersions[filePath] = originContent
        }

        // 获取当前版本
        val codeFileService = CodeFileService.getInstance(project)
        val currentContent = codeFileService.getFileContent(filePath) ?: return false

        // 首先尝试刷新已存在的diff窗口
        if (refreshExistingDiffWindow(filePath, readOriginContent, currentContent)) {
            logger.info("Successfully refreshed existing diff window for file: $filePath")
            return true
        }

        // 如果没有找到已存在的diff窗口或刷新失败，创建新的
        ApplicationManager.getApplication().invokeLater {
            createAndShowDiff(filePath, readOriginContent, currentContent)
            logger.info("Created new diff window for file: $filePath")
        }

        return true
    }

    /**
     * 在源文件窗口中显示内联diff
     *
     * @param filePath 文件路径
     * @return 是否成功显示内联diff
     */
    fun showInlineDiff(filePath: String, originContent: String? = null): Boolean {
        // 获取原始版本
        val readOriginContent = originContent ?: fileVersions[filePath] ?: return false

        if (originContent != null && fileVersions[filePath] == null) {
            fileVersions[filePath] = originContent
        }

        // 使用内联diff服务
        val inlineDiffService = project.service<InlineDiffService>()
        return inlineDiffService.showInlineDiff(filePath, readOriginContent)
    }

    /**
     * 清除指定文件的原始版本记录
     */
    fun clearOriginalVersion(filePath: String) {
        fileVersions.remove(filePath)
        // 同时清理diff窗口跟踪
        val removed = activeDiffRequests.remove(filePath)
        if (removed != null) {
            logger.debug("Cleared diff request reference for file: $filePath")
        }
    }

    /**
     * 判断文件是否有原始版本记录
     */
    fun hasOriginalVersion(filePath: String): Boolean {
        return fileVersions.containsKey(filePath)
    }

    /**
     * 设置文件编辑器监听器，用于清理关闭的diff窗口
     */
    private fun setupFileEditorListener() {
        // 确保先断开之前的连接
        messageBusConnection?.disconnect()

        // 创建新的连接
        messageBusConnection = project.messageBus.connect()

        // 订阅文件编辑器管理器事件
        messageBusConnection?.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
                // 当文件关闭时，检查并清理相关的diff请求
                cleanupDiffRequestsForFile(file)
            }
        })

        logger.debug("File editor listener setup completed")
    }

    /**
     * 清理与指定文件相关的diff请求
     */
    private fun cleanupDiffRequestsForFile(file: VirtualFile) {
        try {
            // 查找与该文件相关的diff请求
            val keysToRemove = mutableListOf<String>()

            for ((key, requestRef) in activeDiffRequests) {
                val request = requestRef.get()
                if (request == null) {
                    // 如果引用已经被GC，移除该条目
                    keysToRemove.add(key)
                    continue
                }

                // 检查文件名是否匹配
                val fileName = file.name
                val requestTitle = request.title ?: ""

                if (requestTitle.contains(fileName)) {
                    keysToRemove.add(key)
                    logger.debug("Found and marked for removal diff request for closed file: $fileName")
                }
            }

            // 移除标记的条目
            for (key in keysToRemove) {
                activeDiffRequests.remove(key)
                logger.debug("Removed diff request for key: $key")
            }

        } catch (e: Exception) {
            logger.warn("Error cleaning up diff requests for file: ${file.path}", e)
        }
    }

    /**
     * 清理服务资源
     */
    fun dispose() {
        // 断开消息总线连接
        messageBusConnection?.disconnect()
        messageBusConnection = null

        // 清空diff请求映射
        activeDiffRequests.clear()

        logger.debug("FileOriginalVersionService disposed")
    }

    companion object {
        /**
         * 获取服务实例
         */
        fun getInstance(project: Project): FileOriginalVersionService = project.service()
    }
}